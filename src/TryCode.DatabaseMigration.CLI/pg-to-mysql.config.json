{
  "Source": {
    "Type": "PostgreSQL",
    "ConnectionString": "Host=***********;Port=5866;Database=ledger_test;User Id=test;Password=**$!4mGo5STvQ3;SslMode=Disable;",
    "BatchSize": 500,
    "CommandTimeout": 180
  },
  "Target": {
    "Type": "MySQL",
    "ConnectionString": "Server=*************;Port=2881;Database=ybt_ledger;User=ybt@test;Password=******;",
    "BatchSize": 200,
    "CommandTimeout": 180
  },
//  "Target": {
//    "Type": "MySQL",
//    "ConnectionString": "Server=************;Port=2881;Database=ybt_ledger;User=ybt@ybt;Password=**************;",
//    "BatchSize": 200,
//    "CommandTimeout": 180
//  },
//  "ExcludedTables": [
//    "AbpAuditLogActions",
//    "AbpAuditLogs",
//    "AbpEntityChanges",
//    "AbpEntityPropertyChanges",
//    "AbpSecurityLogs",
//    "AbpUserClaims",
//    "AppNotifications",
//    "AppUserNotifications",
//    "AppUserSubscribes",
//    "Ledger_LedgerDataUpdateLogs"
//  ],
  "EnableCheckpoint": true,
  "CheckpointFilePath": "pgtomysql_migration_checkpoint.json",
  "SkipSchemaCreation": false,
  "SkipForeignKeys": false,
  "CreateSchemaForExcludedTables": true,
  "MaxDegreeOfParallelism": 2,
  "Logging": {
    "LogLevel": "Information",
    "FilePath": "pgtomysql_migration.log",
    "ConsoleOutput": true
  },
  "TypeMappings": {
    "array": "longtext",
    "_int4": "longtext",
    "_int8": "longtext",
    "_text": "longtext",
    "_varchar": "longtext",
    "_float4": "longtext",
    "_float8": "longtext",
    "_bool": "longtext",
    "_date": "longtext",
    "_timestamp": "longtext",
    "jsonb": "longtext",
    "json": "longtext",
    "uuid": "char(36) CHARACTER SET ascii",
    "timestamp with time zone": "datetime(6)",
    "timestamp without time zone": "datetime(6)",
    "timestamp": "datetime(6)",
    "date": "date",
    "time with time zone": "time(6)",
    "time without time zone": "time(6)",
    "time": "time(6)",
    "bytea": "longblob",
    "interval": "varchar(255)",
    "cidr": "varchar(45)",
    "inet": "varchar(45)",
    "macaddr": "varchar(17)",
    "text": "longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci",
    "character varying": "varchar([num]) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci",
    "varchar": "varchar([num]) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci",
    "char": "char([num]) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci",
    "bit": "bit(64)",
    "bit varying": "varchar(255)",
    "money": "decimal(19,4)",
    "point": "varchar(100)",
    "line": "varchar(255)",
    "lseg": "varchar(255)",
    "box": "varchar(255)",
    "path": "varchar(255)",
    "polygon": "varchar(255)",
    "circle": "varchar(255)",
    "hstore": "longtext",
    "int4range": "varchar(255)",
    "int8range": "varchar(255)",
    "numrange": "varchar(255)",
    "tsrange": "varchar(255)",
    "tstzrange": "varchar(255)",
    "daterange": "varchar(255)",
    "xml": "longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci",
    "unknown": "varchar(4096) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci",
    "integer": "int",
    "bigint": "bigint",
    "boolean": "tinyint(1)",
    "smallint": "smallint",
    "real": "float",
    "double precision": "double",
    "numeric": "decimal(18,6)",
    "decimal": "decimal(18,6)",
    "text[]": "longtext"
  }
}