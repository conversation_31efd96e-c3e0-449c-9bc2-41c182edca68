2025-06-19 11:04:26.891 +08:00 [INF] 开始执行数据库迁移命令，配置文件: pg-to-mysql-ledgerdata.config.json
2025-06-19 11:10:48.743 +08:00 [INF] 获取到8412个表
2025-06-19 11:10:53.568 +08:00 [INF] 发现分区表 LedgerData_LedgerAuditedData，包含 100 个分区
2025-06-19 11:10:59.102 +08:00 [INF] 设置分区表 LedgerData_LedgerAuditedData 的分区数量为 100（基于实际分区数据源数量）
2025-06-19 11:10:59.108 +08:00 [INF] 合并分区表 LedgerData_LedgerAuditedData，总行数: 0，分区数据源: LedgerData_LedgerAuditedData_0(0), LedgerData_LedgerAuditedData_1(0), LedgerData_LedgerAuditedData_10(0), LedgerData_LedgerAuditedData_11(0), LedgerData_LedgerAuditedData_12(0), LedgerData_LedgerAuditedData_13(0), LedgerData_LedgerAuditedData_14(0), LedgerData_LedgerAuditedData_15(0), LedgerData_LedgerAuditedData_16(0), LedgerData_LedgerAuditedData_17(0), LedgerData_LedgerAuditedData_18(0), LedgerData_LedgerAuditedData_19(0), LedgerData_LedgerAuditedData_2(0), LedgerData_LedgerAuditedData_20(0), LedgerData_LedgerAuditedData_21(0), LedgerData_LedgerAuditedData_22(0), LedgerData_LedgerAuditedData_23(0), LedgerData_LedgerAuditedData_24(0), LedgerData_LedgerAuditedData_25(0), LedgerData_LedgerAuditedData_26(0), LedgerData_LedgerAuditedData_27(0), LedgerData_LedgerAuditedData_28(0), LedgerData_LedgerAuditedData_29(0), LedgerData_LedgerAuditedData_3(0), LedgerData_LedgerAuditedData_30(0), LedgerData_LedgerAuditedData_31(0), LedgerData_LedgerAuditedData_32(0), LedgerData_LedgerAuditedData_33(0), LedgerData_LedgerAuditedData_34(0), LedgerData_LedgerAuditedData_35(0), LedgerData_LedgerAuditedData_36(0), LedgerData_LedgerAuditedData_37(0), LedgerData_LedgerAuditedData_38(0), LedgerData_LedgerAuditedData_39(0), LedgerData_LedgerAuditedData_4(0), LedgerData_LedgerAuditedData_40(0), LedgerData_LedgerAuditedData_41(0), LedgerData_LedgerAuditedData_42(0), LedgerData_LedgerAuditedData_43(0), LedgerData_LedgerAuditedData_44(0), LedgerData_LedgerAuditedData_45(0), LedgerData_LedgerAuditedData_46(0), LedgerData_LedgerAuditedData_47(0), LedgerData_LedgerAuditedData_48(0), LedgerData_LedgerAuditedData_49(0), LedgerData_LedgerAuditedData_5(0), LedgerData_LedgerAuditedData_50(0), LedgerData_LedgerAuditedData_51(0), LedgerData_LedgerAuditedData_52(0), LedgerData_LedgerAuditedData_53(0), LedgerData_LedgerAuditedData_54(0), LedgerData_LedgerAuditedData_55(0), LedgerData_LedgerAuditedData_56(0), LedgerData_LedgerAuditedData_57(0), LedgerData_LedgerAuditedData_58(0), LedgerData_LedgerAuditedData_59(0), LedgerData_LedgerAuditedData_6(0), LedgerData_LedgerAuditedData_60(0), LedgerData_LedgerAuditedData_61(0), LedgerData_LedgerAuditedData_62(0), LedgerData_LedgerAuditedData_63(0), LedgerData_LedgerAuditedData_64(0), LedgerData_LedgerAuditedData_65(0), LedgerData_LedgerAuditedData_66(0), LedgerData_LedgerAuditedData_67(0), LedgerData_LedgerAuditedData_68(0), LedgerData_LedgerAuditedData_69(0), LedgerData_LedgerAuditedData_7(0), LedgerData_LedgerAuditedData_70(0), LedgerData_LedgerAuditedData_71(0), LedgerData_LedgerAuditedData_72(0), LedgerData_LedgerAuditedData_73(0), LedgerData_LedgerAuditedData_74(0), LedgerData_LedgerAuditedData_75(0), LedgerData_LedgerAuditedData_76(0), LedgerData_LedgerAuditedData_77(0), LedgerData_LedgerAuditedData_78(0), LedgerData_LedgerAuditedData_79(0), LedgerData_LedgerAuditedData_8(0), LedgerData_LedgerAuditedData_80(0), LedgerData_LedgerAuditedData_81(0), LedgerData_LedgerAuditedData_82(0), LedgerData_LedgerAuditedData_83(0), LedgerData_LedgerAuditedData_84(0), LedgerData_LedgerAuditedData_85(0), LedgerData_LedgerAuditedData_86(0), LedgerData_LedgerAuditedData_87(0), LedgerData_LedgerAuditedData_88(0), LedgerData_LedgerAuditedData_89(0), LedgerData_LedgerAuditedData_9(0), LedgerData_LedgerAuditedData_90(0), LedgerData_LedgerAuditedData_91(0), LedgerData_LedgerAuditedData_92(0), LedgerData_LedgerAuditedData_93(0), LedgerData_LedgerAuditedData_94(0), LedgerData_LedgerAuditedData_95(0), LedgerData_LedgerAuditedData_96(0), LedgerData_LedgerAuditedData_97(0), LedgerData_LedgerAuditedData_98(0), LedgerData_LedgerAuditedData_99(0)
2025-06-19 11:10:59.109 +08:00 [INF] 分区表 LedgerData_LedgerAuditedData 处理完成，将作为单个分区表迁移
2025-06-19 11:10:59.109 +08:00 [INF] 分区表处理完成，原始表数: 101，处理后表数: 1
2025-06-19 11:10:59.109 +08:00 [INF] 过滤后剩余1个表
2025-06-19 11:10:59.109 +08:00 [INF] 使用并行度: 4
2025-06-19 11:10:59.130 +08:00 [INF] 开始处理以下表的迁移: LedgerData_LedgerAuditedData
2025-06-19 11:10:59.130 +08:00 [INF] 表的迁移顺序: LedgerData_LedgerAuditedData
2025-06-19 11:11:00.325 +08:00 [INF] 表 LedgerData_LedgerAuditedData 是分区表，包含 100 个分区，使用分区表迁移逻辑
2025-06-19 11:11:00.329 +08:00 [INF] 开始迁移分区表 LedgerData_LedgerAuditedData，共 100 个分区
2025-06-19 11:11:00.331 +08:00 [INF] 分区表 LedgerData_LedgerAuditedData 包含 100 个数据源
2025-06-19 11:11:00.332 +08:00 [INF] 开始迁移分区数据源 LedgerData_LedgerAuditedData_0（表 LedgerData_LedgerAuditedData）
2025-06-19 11:12:48.224 +08:00 [INF] 开始执行数据库迁移命令，配置文件: pg-to-mysql-ledgerdata.config.json
2025-06-19 11:18:02.062 +08:00 [INF] 获取到8412个表
2025-06-19 11:18:12.046 +08:00 [INF] 发现分区表 LedgerData_LedgerAuditedData，包含 100 个分区
2025-06-19 11:18:13.372 +08:00 [INF] 设置分区表 LedgerData_LedgerAuditedData 的分区数量为 100（基于实际分区数据源数量）
2025-06-19 11:18:13.373 +08:00 [INF] 合并分区表 LedgerData_LedgerAuditedData，总行数: 0，分区数据源: LedgerData_LedgerAuditedData_0(0), LedgerData_LedgerAuditedData_1(0), LedgerData_LedgerAuditedData_10(0), LedgerData_LedgerAuditedData_11(0), LedgerData_LedgerAuditedData_12(0), LedgerData_LedgerAuditedData_13(0), LedgerData_LedgerAuditedData_14(0), LedgerData_LedgerAuditedData_15(0), LedgerData_LedgerAuditedData_16(0), LedgerData_LedgerAuditedData_17(0), LedgerData_LedgerAuditedData_18(0), LedgerData_LedgerAuditedData_19(0), LedgerData_LedgerAuditedData_2(0), LedgerData_LedgerAuditedData_20(0), LedgerData_LedgerAuditedData_21(0), LedgerData_LedgerAuditedData_22(0), LedgerData_LedgerAuditedData_23(0), LedgerData_LedgerAuditedData_24(0), LedgerData_LedgerAuditedData_25(0), LedgerData_LedgerAuditedData_26(0), LedgerData_LedgerAuditedData_27(0), LedgerData_LedgerAuditedData_28(0), LedgerData_LedgerAuditedData_29(0), LedgerData_LedgerAuditedData_3(0), LedgerData_LedgerAuditedData_30(0), LedgerData_LedgerAuditedData_31(0), LedgerData_LedgerAuditedData_32(0), LedgerData_LedgerAuditedData_33(0), LedgerData_LedgerAuditedData_34(0), LedgerData_LedgerAuditedData_35(0), LedgerData_LedgerAuditedData_36(0), LedgerData_LedgerAuditedData_37(0), LedgerData_LedgerAuditedData_38(0), LedgerData_LedgerAuditedData_39(0), LedgerData_LedgerAuditedData_4(0), LedgerData_LedgerAuditedData_40(0), LedgerData_LedgerAuditedData_41(0), LedgerData_LedgerAuditedData_42(0), LedgerData_LedgerAuditedData_43(0), LedgerData_LedgerAuditedData_44(0), LedgerData_LedgerAuditedData_45(0), LedgerData_LedgerAuditedData_46(0), LedgerData_LedgerAuditedData_47(0), LedgerData_LedgerAuditedData_48(0), LedgerData_LedgerAuditedData_49(0), LedgerData_LedgerAuditedData_5(0), LedgerData_LedgerAuditedData_50(0), LedgerData_LedgerAuditedData_51(0), LedgerData_LedgerAuditedData_52(0), LedgerData_LedgerAuditedData_53(0), LedgerData_LedgerAuditedData_54(0), LedgerData_LedgerAuditedData_55(0), LedgerData_LedgerAuditedData_56(0), LedgerData_LedgerAuditedData_57(0), LedgerData_LedgerAuditedData_58(0), LedgerData_LedgerAuditedData_59(0), LedgerData_LedgerAuditedData_6(0), LedgerData_LedgerAuditedData_60(0), LedgerData_LedgerAuditedData_61(0), LedgerData_LedgerAuditedData_62(0), LedgerData_LedgerAuditedData_63(0), LedgerData_LedgerAuditedData_64(0), LedgerData_LedgerAuditedData_65(0), LedgerData_LedgerAuditedData_66(0), LedgerData_LedgerAuditedData_67(0), LedgerData_LedgerAuditedData_68(0), LedgerData_LedgerAuditedData_69(0), LedgerData_LedgerAuditedData_7(0), LedgerData_LedgerAuditedData_70(0), LedgerData_LedgerAuditedData_71(0), LedgerData_LedgerAuditedData_72(0), LedgerData_LedgerAuditedData_73(0), LedgerData_LedgerAuditedData_74(0), LedgerData_LedgerAuditedData_75(0), LedgerData_LedgerAuditedData_76(0), LedgerData_LedgerAuditedData_77(0), LedgerData_LedgerAuditedData_78(0), LedgerData_LedgerAuditedData_79(0), LedgerData_LedgerAuditedData_8(0), LedgerData_LedgerAuditedData_80(0), LedgerData_LedgerAuditedData_81(0), LedgerData_LedgerAuditedData_82(0), LedgerData_LedgerAuditedData_83(0), LedgerData_LedgerAuditedData_84(0), LedgerData_LedgerAuditedData_85(0), LedgerData_LedgerAuditedData_86(0), LedgerData_LedgerAuditedData_87(0), LedgerData_LedgerAuditedData_88(0), LedgerData_LedgerAuditedData_89(0), LedgerData_LedgerAuditedData_9(0), LedgerData_LedgerAuditedData_90(0), LedgerData_LedgerAuditedData_91(0), LedgerData_LedgerAuditedData_92(0), LedgerData_LedgerAuditedData_93(0), LedgerData_LedgerAuditedData_94(0), LedgerData_LedgerAuditedData_95(0), LedgerData_LedgerAuditedData_96(0), LedgerData_LedgerAuditedData_97(0), LedgerData_LedgerAuditedData_98(0), LedgerData_LedgerAuditedData_99(0)
2025-06-19 11:18:15.186 +08:00 [INF] 分区表 LedgerData_LedgerAuditedData 处理完成，将作为单个分区表迁移
2025-06-19 11:18:19.632 +08:00 [INF] 分区表处理完成，原始表数: 101，处理后表数: 1
2025-06-19 11:18:23.940 +08:00 [INF] 过滤后剩余1个表
2025-06-19 11:18:25.804 +08:00 [INF] 使用并行度: 4
2025-06-19 11:18:31.886 +08:00 [INF] 开始处理以下表的迁移: LedgerData_LedgerAuditedData
2025-06-19 11:18:31.886 +08:00 [INF] 表的迁移顺序: LedgerData_LedgerAuditedData
2025-06-19 11:19:20.716 +08:00 [INF] 表 LedgerData_LedgerAuditedData 是分区表，包含 100 个分区，使用分区表迁移逻辑
2025-06-19 11:19:35.078 +08:00 [INF] 开始迁移分区表 LedgerData_LedgerAuditedData，共 100 个分区
2025-06-19 11:19:49.165 +08:00 [INF] 分区表 LedgerData_LedgerAuditedData 包含 100 个数据源
2025-06-19 11:19:53.755 +08:00 [INF] 开始迁移分区数据源 LedgerData_LedgerAuditedData_0（表 LedgerData_LedgerAuditedData）
2025-06-19 11:21:46.572 +08:00 [INF] 开始执行数据库迁移命令，配置文件: pg-to-mysql-ledgerdata.config.json
2025-06-19 11:26:47.563 +08:00 [INF] 获取到8412个表
2025-06-19 11:26:47.601 +08:00 [INF] 发现分区表 LedgerData_LedgerAuditedData，包含 100 个分区
2025-06-19 11:26:47.604 +08:00 [INF] 设置分区表 LedgerData_LedgerAuditedData 的分区数量为 100（基于实际分区数据源数量）
2025-06-19 11:26:47.604 +08:00 [INF] 合并分区表 LedgerData_LedgerAuditedData，总行数: 0，分区数据源: LedgerData_LedgerAuditedData_0(0), LedgerData_LedgerAuditedData_1(0), LedgerData_LedgerAuditedData_10(0), LedgerData_LedgerAuditedData_11(0), LedgerData_LedgerAuditedData_12(0), LedgerData_LedgerAuditedData_13(0), LedgerData_LedgerAuditedData_14(0), LedgerData_LedgerAuditedData_15(0), LedgerData_LedgerAuditedData_16(0), LedgerData_LedgerAuditedData_17(0), LedgerData_LedgerAuditedData_18(0), LedgerData_LedgerAuditedData_19(0), LedgerData_LedgerAuditedData_2(0), LedgerData_LedgerAuditedData_20(0), LedgerData_LedgerAuditedData_21(0), LedgerData_LedgerAuditedData_22(0), LedgerData_LedgerAuditedData_23(0), LedgerData_LedgerAuditedData_24(0), LedgerData_LedgerAuditedData_25(0), LedgerData_LedgerAuditedData_26(0), LedgerData_LedgerAuditedData_27(0), LedgerData_LedgerAuditedData_28(0), LedgerData_LedgerAuditedData_29(0), LedgerData_LedgerAuditedData_3(0), LedgerData_LedgerAuditedData_30(0), LedgerData_LedgerAuditedData_31(0), LedgerData_LedgerAuditedData_32(0), LedgerData_LedgerAuditedData_33(0), LedgerData_LedgerAuditedData_34(0), LedgerData_LedgerAuditedData_35(0), LedgerData_LedgerAuditedData_36(0), LedgerData_LedgerAuditedData_37(0), LedgerData_LedgerAuditedData_38(0), LedgerData_LedgerAuditedData_39(0), LedgerData_LedgerAuditedData_4(0), LedgerData_LedgerAuditedData_40(0), LedgerData_LedgerAuditedData_41(0), LedgerData_LedgerAuditedData_42(0), LedgerData_LedgerAuditedData_43(0), LedgerData_LedgerAuditedData_44(0), LedgerData_LedgerAuditedData_45(0), LedgerData_LedgerAuditedData_46(0), LedgerData_LedgerAuditedData_47(0), LedgerData_LedgerAuditedData_48(0), LedgerData_LedgerAuditedData_49(0), LedgerData_LedgerAuditedData_5(0), LedgerData_LedgerAuditedData_50(0), LedgerData_LedgerAuditedData_51(0), LedgerData_LedgerAuditedData_52(0), LedgerData_LedgerAuditedData_53(0), LedgerData_LedgerAuditedData_54(0), LedgerData_LedgerAuditedData_55(0), LedgerData_LedgerAuditedData_56(0), LedgerData_LedgerAuditedData_57(0), LedgerData_LedgerAuditedData_58(0), LedgerData_LedgerAuditedData_59(0), LedgerData_LedgerAuditedData_6(0), LedgerData_LedgerAuditedData_60(0), LedgerData_LedgerAuditedData_61(0), LedgerData_LedgerAuditedData_62(0), LedgerData_LedgerAuditedData_63(0), LedgerData_LedgerAuditedData_64(0), LedgerData_LedgerAuditedData_65(0), LedgerData_LedgerAuditedData_66(0), LedgerData_LedgerAuditedData_67(0), LedgerData_LedgerAuditedData_68(0), LedgerData_LedgerAuditedData_69(0), LedgerData_LedgerAuditedData_7(0), LedgerData_LedgerAuditedData_70(0), LedgerData_LedgerAuditedData_71(0), LedgerData_LedgerAuditedData_72(0), LedgerData_LedgerAuditedData_73(0), LedgerData_LedgerAuditedData_74(0), LedgerData_LedgerAuditedData_75(0), LedgerData_LedgerAuditedData_76(0), LedgerData_LedgerAuditedData_77(0), LedgerData_LedgerAuditedData_78(0), LedgerData_LedgerAuditedData_79(0), LedgerData_LedgerAuditedData_8(0), LedgerData_LedgerAuditedData_80(0), LedgerData_LedgerAuditedData_81(0), LedgerData_LedgerAuditedData_82(0), LedgerData_LedgerAuditedData_83(0), LedgerData_LedgerAuditedData_84(0), LedgerData_LedgerAuditedData_85(0), LedgerData_LedgerAuditedData_86(0), LedgerData_LedgerAuditedData_87(0), LedgerData_LedgerAuditedData_88(0), LedgerData_LedgerAuditedData_89(0), LedgerData_LedgerAuditedData_9(0), LedgerData_LedgerAuditedData_90(0), LedgerData_LedgerAuditedData_91(0), LedgerData_LedgerAuditedData_92(0), LedgerData_LedgerAuditedData_93(0), LedgerData_LedgerAuditedData_94(0), LedgerData_LedgerAuditedData_95(0), LedgerData_LedgerAuditedData_96(0), LedgerData_LedgerAuditedData_97(0), LedgerData_LedgerAuditedData_98(0), LedgerData_LedgerAuditedData_99(0)
2025-06-19 11:26:47.604 +08:00 [INF] 分区表 LedgerData_LedgerAuditedData 处理完成，将作为单个分区表迁移
2025-06-19 11:26:47.604 +08:00 [INF] 分区表处理完成，原始表数: 101，处理后表数: 1
2025-06-19 11:26:47.604 +08:00 [INF] 过滤后剩余1个表
2025-06-19 11:26:47.604 +08:00 [INF] 使用并行度: 4
2025-06-19 11:26:47.616 +08:00 [INF] 开始处理以下表的迁移: LedgerData_LedgerAuditedData
2025-06-19 11:26:47.616 +08:00 [INF] 表的迁移顺序: LedgerData_LedgerAuditedData
2025-06-19 11:26:47.628 +08:00 [INF] 表 LedgerData_LedgerAuditedData 是分区表，包含 100 个分区，使用分区表迁移逻辑
2025-06-19 11:26:47.648 +08:00 [INF] 开始迁移分区表 LedgerData_LedgerAuditedData，共 100 个分区
2025-06-19 11:26:47.650 +08:00 [INF] 分区表 LedgerData_LedgerAuditedData 包含 100 个数据源
2025-06-19 11:26:47.650 +08:00 [INF] 开始迁移分区数据源 LedgerData_LedgerAuditedData_0（表 LedgerData_LedgerAuditedData）
2025-06-19 11:40:53.441 +08:00 [INF] 开始执行数据库迁移命令，配置文件: pg-to-mysql-ledgerdata.config.json
2025-06-19 11:45:56.739 +08:00 [INF] 获取到8412个表
2025-06-19 11:45:56.777 +08:00 [INF] 发现分区表 LedgerData_LedgerAuditedData，包含 100 个分区
2025-06-19 11:45:56.781 +08:00 [INF] 设置分区表 LedgerData_LedgerAuditedData 的分区数量为 100（基于实际分区数据源数量）
2025-06-19 11:45:56.781 +08:00 [INF] 合并分区表 LedgerData_LedgerAuditedData，总行数: 0，分区数据源: LedgerData_LedgerAuditedData_0(0), LedgerData_LedgerAuditedData_1(0), LedgerData_LedgerAuditedData_10(0), LedgerData_LedgerAuditedData_11(0), LedgerData_LedgerAuditedData_12(0), LedgerData_LedgerAuditedData_13(0), LedgerData_LedgerAuditedData_14(0), LedgerData_LedgerAuditedData_15(0), LedgerData_LedgerAuditedData_16(0), LedgerData_LedgerAuditedData_17(0), LedgerData_LedgerAuditedData_18(0), LedgerData_LedgerAuditedData_19(0), LedgerData_LedgerAuditedData_2(0), LedgerData_LedgerAuditedData_20(0), LedgerData_LedgerAuditedData_21(0), LedgerData_LedgerAuditedData_22(0), LedgerData_LedgerAuditedData_23(0), LedgerData_LedgerAuditedData_24(0), LedgerData_LedgerAuditedData_25(0), LedgerData_LedgerAuditedData_26(0), LedgerData_LedgerAuditedData_27(0), LedgerData_LedgerAuditedData_28(0), LedgerData_LedgerAuditedData_29(0), LedgerData_LedgerAuditedData_3(0), LedgerData_LedgerAuditedData_30(0), LedgerData_LedgerAuditedData_31(0), LedgerData_LedgerAuditedData_32(0), LedgerData_LedgerAuditedData_33(0), LedgerData_LedgerAuditedData_34(0), LedgerData_LedgerAuditedData_35(0), LedgerData_LedgerAuditedData_36(0), LedgerData_LedgerAuditedData_37(0), LedgerData_LedgerAuditedData_38(0), LedgerData_LedgerAuditedData_39(0), LedgerData_LedgerAuditedData_4(0), LedgerData_LedgerAuditedData_40(0), LedgerData_LedgerAuditedData_41(0), LedgerData_LedgerAuditedData_42(0), LedgerData_LedgerAuditedData_43(0), LedgerData_LedgerAuditedData_44(0), LedgerData_LedgerAuditedData_45(0), LedgerData_LedgerAuditedData_46(0), LedgerData_LedgerAuditedData_47(0), LedgerData_LedgerAuditedData_48(0), LedgerData_LedgerAuditedData_49(0), LedgerData_LedgerAuditedData_5(0), LedgerData_LedgerAuditedData_50(0), LedgerData_LedgerAuditedData_51(0), LedgerData_LedgerAuditedData_52(0), LedgerData_LedgerAuditedData_53(0), LedgerData_LedgerAuditedData_54(0), LedgerData_LedgerAuditedData_55(0), LedgerData_LedgerAuditedData_56(0), LedgerData_LedgerAuditedData_57(0), LedgerData_LedgerAuditedData_58(0), LedgerData_LedgerAuditedData_59(0), LedgerData_LedgerAuditedData_6(0), LedgerData_LedgerAuditedData_60(0), LedgerData_LedgerAuditedData_61(0), LedgerData_LedgerAuditedData_62(0), LedgerData_LedgerAuditedData_63(0), LedgerData_LedgerAuditedData_64(0), LedgerData_LedgerAuditedData_65(0), LedgerData_LedgerAuditedData_66(0), LedgerData_LedgerAuditedData_67(0), LedgerData_LedgerAuditedData_68(0), LedgerData_LedgerAuditedData_69(0), LedgerData_LedgerAuditedData_7(0), LedgerData_LedgerAuditedData_70(0), LedgerData_LedgerAuditedData_71(0), LedgerData_LedgerAuditedData_72(0), LedgerData_LedgerAuditedData_73(0), LedgerData_LedgerAuditedData_74(0), LedgerData_LedgerAuditedData_75(0), LedgerData_LedgerAuditedData_76(0), LedgerData_LedgerAuditedData_77(0), LedgerData_LedgerAuditedData_78(0), LedgerData_LedgerAuditedData_79(0), LedgerData_LedgerAuditedData_8(0), LedgerData_LedgerAuditedData_80(0), LedgerData_LedgerAuditedData_81(0), LedgerData_LedgerAuditedData_82(0), LedgerData_LedgerAuditedData_83(0), LedgerData_LedgerAuditedData_84(0), LedgerData_LedgerAuditedData_85(0), LedgerData_LedgerAuditedData_86(0), LedgerData_LedgerAuditedData_87(0), LedgerData_LedgerAuditedData_88(0), LedgerData_LedgerAuditedData_89(0), LedgerData_LedgerAuditedData_9(0), LedgerData_LedgerAuditedData_90(0), LedgerData_LedgerAuditedData_91(0), LedgerData_LedgerAuditedData_92(0), LedgerData_LedgerAuditedData_93(0), LedgerData_LedgerAuditedData_94(0), LedgerData_LedgerAuditedData_95(0), LedgerData_LedgerAuditedData_96(0), LedgerData_LedgerAuditedData_97(0), LedgerData_LedgerAuditedData_98(0), LedgerData_LedgerAuditedData_99(0)
2025-06-19 11:45:56.782 +08:00 [INF] 分区表 LedgerData_LedgerAuditedData 处理完成，将作为单个分区表迁移
2025-06-19 11:45:56.782 +08:00 [INF] 分区表处理完成，原始表数: 101，处理后表数: 1
2025-06-19 11:45:56.782 +08:00 [INF] 过滤后剩余1个表
2025-06-19 11:45:56.782 +08:00 [INF] 使用并行度: 4
2025-06-19 11:45:56.797 +08:00 [INF] 开始处理以下表的迁移: LedgerData_LedgerAuditedData
2025-06-19 11:45:56.797 +08:00 [INF] 表的迁移顺序: LedgerData_LedgerAuditedData
2025-06-19 11:45:56.810 +08:00 [INF] 表 LedgerData_LedgerAuditedData 是分区表，包含 100 个分区，使用分区表迁移逻辑
2025-06-19 11:45:56.828 +08:00 [INF] 开始迁移分区表 LedgerData_LedgerAuditedData，共 100 个分区
2025-06-19 11:45:56.829 +08:00 [INF] 分区表 LedgerData_LedgerAuditedData 包含 100 个数据源
2025-06-19 11:45:56.830 +08:00 [INF] 开始迁移分区数据源 LedgerData_LedgerAuditedData_0（表 LedgerData_LedgerAuditedData）
2025-06-19 11:51:32.492 +08:00 [INF] 开始执行数据库迁移命令，配置文件: pg-to-mysql-ledgerdata.config.json
2025-06-19 11:56:39.730 +08:00 [INF] 获取到8412个表
2025-06-19 11:56:39.796 +08:00 [INF] 发现分区表 LedgerData_LedgerAuditedData，包含 100 个分区
2025-06-19 11:56:39.800 +08:00 [INF] 设置分区表 LedgerData_LedgerAuditedData 的分区数量为 100（基于实际分区数据源数量）
2025-06-19 11:56:39.800 +08:00 [INF] 合并分区表 LedgerData_LedgerAuditedData，总行数: 0，分区数据源: LedgerData_LedgerAuditedData_0(0), LedgerData_LedgerAuditedData_1(0), LedgerData_LedgerAuditedData_10(0), LedgerData_LedgerAuditedData_11(0), LedgerData_LedgerAuditedData_12(0), LedgerData_LedgerAuditedData_13(0), LedgerData_LedgerAuditedData_14(0), LedgerData_LedgerAuditedData_15(0), LedgerData_LedgerAuditedData_16(0), LedgerData_LedgerAuditedData_17(0), LedgerData_LedgerAuditedData_18(0), LedgerData_LedgerAuditedData_19(0), LedgerData_LedgerAuditedData_2(0), LedgerData_LedgerAuditedData_20(0), LedgerData_LedgerAuditedData_21(0), LedgerData_LedgerAuditedData_22(0), LedgerData_LedgerAuditedData_23(0), LedgerData_LedgerAuditedData_24(0), LedgerData_LedgerAuditedData_25(0), LedgerData_LedgerAuditedData_26(0), LedgerData_LedgerAuditedData_27(0), LedgerData_LedgerAuditedData_28(0), LedgerData_LedgerAuditedData_29(0), LedgerData_LedgerAuditedData_3(0), LedgerData_LedgerAuditedData_30(0), LedgerData_LedgerAuditedData_31(0), LedgerData_LedgerAuditedData_32(0), LedgerData_LedgerAuditedData_33(0), LedgerData_LedgerAuditedData_34(0), LedgerData_LedgerAuditedData_35(0), LedgerData_LedgerAuditedData_36(0), LedgerData_LedgerAuditedData_37(0), LedgerData_LedgerAuditedData_38(0), LedgerData_LedgerAuditedData_39(0), LedgerData_LedgerAuditedData_4(0), LedgerData_LedgerAuditedData_40(0), LedgerData_LedgerAuditedData_41(0), LedgerData_LedgerAuditedData_42(0), LedgerData_LedgerAuditedData_43(0), LedgerData_LedgerAuditedData_44(0), LedgerData_LedgerAuditedData_45(0), LedgerData_LedgerAuditedData_46(0), LedgerData_LedgerAuditedData_47(0), LedgerData_LedgerAuditedData_48(0), LedgerData_LedgerAuditedData_49(0), LedgerData_LedgerAuditedData_5(0), LedgerData_LedgerAuditedData_50(0), LedgerData_LedgerAuditedData_51(0), LedgerData_LedgerAuditedData_52(0), LedgerData_LedgerAuditedData_53(0), LedgerData_LedgerAuditedData_54(0), LedgerData_LedgerAuditedData_55(0), LedgerData_LedgerAuditedData_56(0), LedgerData_LedgerAuditedData_57(0), LedgerData_LedgerAuditedData_58(0), LedgerData_LedgerAuditedData_59(0), LedgerData_LedgerAuditedData_6(0), LedgerData_LedgerAuditedData_60(0), LedgerData_LedgerAuditedData_61(0), LedgerData_LedgerAuditedData_62(0), LedgerData_LedgerAuditedData_63(0), LedgerData_LedgerAuditedData_64(0), LedgerData_LedgerAuditedData_65(0), LedgerData_LedgerAuditedData_66(0), LedgerData_LedgerAuditedData_67(0), LedgerData_LedgerAuditedData_68(0), LedgerData_LedgerAuditedData_69(0), LedgerData_LedgerAuditedData_7(0), LedgerData_LedgerAuditedData_70(0), LedgerData_LedgerAuditedData_71(0), LedgerData_LedgerAuditedData_72(0), LedgerData_LedgerAuditedData_73(0), LedgerData_LedgerAuditedData_74(0), LedgerData_LedgerAuditedData_75(0), LedgerData_LedgerAuditedData_76(0), LedgerData_LedgerAuditedData_77(0), LedgerData_LedgerAuditedData_78(0), LedgerData_LedgerAuditedData_79(0), LedgerData_LedgerAuditedData_8(0), LedgerData_LedgerAuditedData_80(0), LedgerData_LedgerAuditedData_81(0), LedgerData_LedgerAuditedData_82(0), LedgerData_LedgerAuditedData_83(0), LedgerData_LedgerAuditedData_84(0), LedgerData_LedgerAuditedData_85(0), LedgerData_LedgerAuditedData_86(0), LedgerData_LedgerAuditedData_87(0), LedgerData_LedgerAuditedData_88(0), LedgerData_LedgerAuditedData_89(0), LedgerData_LedgerAuditedData_9(0), LedgerData_LedgerAuditedData_90(0), LedgerData_LedgerAuditedData_91(0), LedgerData_LedgerAuditedData_92(0), LedgerData_LedgerAuditedData_93(0), LedgerData_LedgerAuditedData_94(0), LedgerData_LedgerAuditedData_95(0), LedgerData_LedgerAuditedData_96(0), LedgerData_LedgerAuditedData_97(0), LedgerData_LedgerAuditedData_98(0), LedgerData_LedgerAuditedData_99(0)
2025-06-19 11:56:39.800 +08:00 [INF] 分区表 LedgerData_LedgerAuditedData 处理完成，将作为单个分区表迁移
2025-06-19 11:56:39.800 +08:00 [INF] 分区表处理完成，原始表数: 101，处理后表数: 1
2025-06-19 11:56:39.800 +08:00 [INF] 过滤后剩余1个表
2025-06-19 11:56:39.800 +08:00 [INF] 使用并行度: 4
2025-06-19 11:56:39.815 +08:00 [INF] 开始处理以下表的迁移: LedgerData_LedgerAuditedData
2025-06-19 11:56:39.815 +08:00 [INF] 表的迁移顺序: LedgerData_LedgerAuditedData
2025-06-19 11:56:39.826 +08:00 [INF] 表 LedgerData_LedgerAuditedData 是分区表，包含 100 个分区，使用分区表迁移逻辑
2025-06-19 11:56:39.867 +08:00 [INF] 开始迁移分区表 LedgerData_LedgerAuditedData，共 100 个分区
2025-06-19 11:56:39.869 +08:00 [INF] 分区表 LedgerData_LedgerAuditedData 包含 100 个数据源
2025-06-19 11:56:39.869 +08:00 [INF] 开始迁移分区数据源 LedgerData_LedgerAuditedData_0（表 LedgerData_LedgerAuditedData）
2025-06-19 11:57:20.502 +08:00 [INF] 分区数据源 LedgerData_LedgerAuditedData_0 共有 112 行数据
2025-06-19 11:57:35.284 +08:00 [INF] 分区数据源 LedgerData_LedgerAuditedData_0 迁移完成，共迁移 112 行
2025-06-19 11:57:35.284 +08:00 [INF] 开始迁移分区数据源 LedgerData_LedgerAuditedData_1（表 LedgerData_LedgerAuditedData）
2025-06-19 11:57:37.735 +08:00 [INF] 分区数据源 LedgerData_LedgerAuditedData_1 共有 586 行数据
