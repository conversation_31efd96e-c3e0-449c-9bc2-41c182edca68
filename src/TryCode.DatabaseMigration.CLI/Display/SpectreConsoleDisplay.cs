using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;
using Spectre.Console;
using TryCode.DatabaseMigration.Core.Models;

namespace TryCode.DatabaseMigration.CLI.Display
{
    /// <summary>
    /// 使用Spectre.Console实现的控制台显示
    /// </summary>
    public class SpectreConsoleDisplay : IProgress<MigrationProgress>, IDisposable
    {
        private readonly ConcurrentDictionary<string, TableStatus> _tableStatuses = new();
        private readonly Timer _refreshTimer;
        private readonly bool _verbose;
        private readonly object _updateLock = new();
        private bool _disposed;
        private readonly List<string> _completedTables = new();
        private readonly List<string> _failedTables = new();
        private readonly Table _statusTable;
        private readonly Table _summaryTable;
        private readonly Layout _layout;
        private bool _isFirstUpdate = true;
        private DateTime _startTime;

        /// <summary>
        /// 表状态
        /// </summary>
        private class TableStatus
        {
            public string TableName { get; init; }= string.Empty;
            public string Stage { get; set; } = string.Empty;
            public string Status { get; set; } = string.Empty;
            public long Total { get; set; }
            public long Processed { get; set; }
            public double Percentage => Total <= 0 ? 0 : Math.Round((double)Processed / Total * 100, 2);
            public DateTime LastUpdate { get; set; } = DateTime.Now;
        }

        /// <summary>
        /// 初始化新的<see cref="SpectreConsoleDisplay"/>实例
        /// </summary>
        /// <param name="verbose">是否显示详细信息</param>
        public SpectreConsoleDisplay(bool verbose = false)
        {
            _verbose = verbose;
            _startTime = DateTime.Now;

            // 创建状态表
            _statusTable = new Table()
                .AddColumn(new TableColumn("表名").Width(30))
                .AddColumn(new TableColumn("阶段").Width(15))
                .AddColumn(new TableColumn("状态").Width(20))
                .AddColumn(new TableColumn("进度").Width(20))
                .Border(TableBorder.Rounded);

            // 创建摘要表
            _summaryTable = new Table()
                .AddColumn(new TableColumn("总表数").Width(10))
                .AddColumn(new TableColumn("已完成").Width(10))
                .AddColumn(new TableColumn("失败").Width(10))
                .AddColumn(new TableColumn("已用时间").Width(15))
                .Border(TableBorder.Rounded);

            // 创建布局
            _layout = new Layout()
                .SplitRows(
                    new Layout("Header").Size(3),
                    new Layout("Tables"),
                    new Layout("Summary").Size(5)
                );

            // 设置刷新定时器 (300ms)
            _refreshTimer = new Timer(_ => RefreshDisplay(), null, 0, 300);

            // 清除控制台
            Console.Clear();
        }

        /// <summary>
        /// 报告进度
        /// </summary>
        public void Report(MigrationProgress value)
        {
            if (_disposed) return;

            lock (_updateLock)
            {
                if (_isFirstUpdate)
                {
                    _isFirstUpdate = false;
                    _startTime = DateTime.Now;
                }

                // 更新或创建表状态
                _tableStatuses.AddOrUpdate(
                    value.TableName,
                    // 添加新表状态
                    _ => new TableStatus
                    {
                        TableName = value.TableName,
                        Stage = value.Stage,
                        Status = value.Status,
                        Total = value.Total,
                        Processed = value.Processed,
                        LastUpdate = DateTime.Now
                    },
                    // 更新现有表状态
                    (_, existing) =>
                    {
                        existing.Stage = value.Stage;
                        existing.Status = value.Status;
                        existing.Total = value.Total;
                        existing.Processed = value.Processed;
                        existing.LastUpdate = DateTime.Now;
                        return existing;
                    });

                // 更新完成和失败的表
                if ((value.Status.Contains("完成") || (value.Total > 0 && value.Processed >= value.Total)) &&
                    !_completedTables.Contains(value.TableName))
                {
                    _completedTables.Add(value.TableName);
                }
                else if (value.Status.Contains("失败") && !_failedTables.Contains(value.TableName))
                {
                    _failedTables.Add(value.TableName);
                }
            }
        }

        /// <summary>
        /// 刷新显示
        /// </summary>
        private void RefreshDisplay()
        {
            if (_disposed) return;

            lock (_updateLock)
            {
                // 构建表格
                _statusTable.Rows.Clear();

                // 对表状态进行排序：正在迁移的表排在前面，然后是已完成的表，最后是失败的表
                var sortedStatuses = _tableStatuses.Values
                    .OrderBy(status => {
                        // 正在迁移的表优先级最高
                        if (!status.Status.Contains("完成") && !status.Status.Contains("失败") &&
                            (status.Total == 0 || status.Processed < status.Total))
                            return 0;
                        // 已完成的表优先级其次 (包括那些已经迁移了所有数据但状态未更新的表)
                        else if (status.Status.Contains("完成") ||
                                (status.Total > 0 && status.Processed >= status.Total))
                            return 1;
                        // 失败的表优先级最低
                        else
                            return 2;
                    })
                    .ThenBy(status => status.TableName); // 相同优先级下按表名排序

                foreach (var tableStatus in sortedStatuses)
                {
                    var progressText = tableStatus.Total > 0
                        ? $"{tableStatus.Processed}/{tableStatus.Total} ({tableStatus.Percentage:F1}%)"
                        : "-";

                    var stageText = tableStatus.Stage;
                    var statusText = tableStatus.Status;

                    // 如果表已经迁移了所有数据但状态未更新，则显示为已完成
                    if (tableStatus.Total > 0 && tableStatus.Processed >= tableStatus.Total &&
                        !statusText.Contains("完成") && !statusText.Contains("失败"))
                    {
                        statusText = $"已迁移 {tableStatus.Processed}/{tableStatus.Total} 行";
                    }

                    // 根据状态设置颜色
                    var tableNameText = tableStatus.Status.Contains("失败")
                        ? $"[red]{tableStatus.TableName}[/]"
                        : tableStatus.Status.Contains("完成") || (tableStatus.Total > 0 && tableStatus.Processed >= tableStatus.Total)
                            ? $"[green]{tableStatus.TableName}[/]"
                            : $"[yellow]{tableStatus.TableName}[/]"; // 正在迁移的表标为黄色

                    _statusTable.AddRow(tableNameText, stageText, statusText, progressText);
                }

                // 更新摘要
                _summaryTable.Rows.Clear();
                var totalTables = _tableStatuses.Count;

                // 重新计算已完成的表数量，包括那些已经迁移了所有数据但状态未更新的表
                var actualCompletedTables = _tableStatuses.Values.Count(s =>
                    s.Status.Contains("完成") || (s.Total > 0 && s.Processed >= s.Total));

                var completed = Math.Max(_completedTables.Count, actualCompletedTables);
                var failed = _failedTables.Count;
                var elapsedTime = DateTime.Now - _startTime;
                var elapsedText = elapsedTime.TotalHours >= 1
                    ? $"{elapsedTime.Hours}小时{elapsedTime.Minutes}分钟"
                    : elapsedTime.TotalMinutes >= 1
                        ? $"{elapsedTime.Minutes}分钟{elapsedTime.Seconds}秒"
                        : $"{elapsedTime.Seconds}秒";

                _summaryTable.AddRow(
                    totalTables.ToString(),
                    $"[green]{completed}[/]",
                    $"[red]{failed}[/]",
                    elapsedText);

                // 更新布局
                _layout["Header"].Update(
                    new Markup("[bold yellow]数据库迁移进度[/]").Centered());
                _layout["Tables"].Update(_statusTable);
                _layout["Summary"].Update(_summaryTable);

                // 渲染
                AnsiConsole.Clear();
                AnsiConsole.Write(_layout);
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed) return;
            _disposed = true;

            // 停止刷新定时器
            _refreshTimer?.Dispose();

            // 显示最终结果
            RefreshDisplay();

            GC.SuppressFinalize(this);
        }
    }
}
